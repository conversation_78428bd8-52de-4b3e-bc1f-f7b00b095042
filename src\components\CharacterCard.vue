<template>

</template>

<script setup lang="ts">
import { ref } from 'vue'
import { CheckOutlined } from '@ant-design/icons-vue'
import type { Character } from '@/stores/characterStore'
import { useSkillStore } from '@/stores/skillStore'
import CharacterDetails from './CharacterDetails.vue'

interface Props {
  character: Character
  selected?: boolean
  selectable?: boolean
  disabled?: boolean
}

interface Emits {
  (e: 'select', character: Character): void
  (e: 'click', character: Character): void
}

const props = withDefaults(defineProps<Props>(), {
  selected: false,
  selectable: false,
  disabled: false
})

const emit = defineEmits<Emits>()

const skillStore = useSkillStore()
const showDetails = ref(false)

// 处理卡片点击
const handleCardClick = () => {
  if (props.disabled) return

  if (props.selectable) {
    emit('select', props.character)
  } else {
    emit('click', props.character)
  }
}

// 获取状态文本
const getStatusText = (status: string): string => {
  const statusMap: Record<string, string> = {
    'Normal': '正常',
    'Tired': '疲惫',
    'Overworked': '过劳',
    'Resting': '休息',
    'Exiled': '流放'
  }
  return statusMap[status] || status
}

// 获取类型文本
const getTypeText = (type: string): string => {
  const typeMap: Record<string, string> = {
    'Normal': '普通',
    'Rare': '稀有',
    'Special': '特殊'
  }
  return typeMap[type] || type
}

// 获取技能名称
const getSkillName = (skillId: string): string => {
  const skill = skillStore.getSkillById(skillId)
  return skill ? skill.name : skillId
}

// 获取技能颜色
const getSkillColor = (skillId: string): string => {
  const skill = skillStore.getSkillById(skillId)
  if (!skill) return 'default'
  return skill.rarity === 'Rare' ? 'purple' : 'blue'
}
</script>

<style scoped></style>
