<template>
  <div class="bedroom-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <a-button type="text" @click="router.back()" class="back-button">
        <template #icon>
          <ArrowLeftOutlined />
        </template>
        返回
      </a-button>
      <h1>🛏️ 寝室</h1>
      <p class="subtitle">休息恢复，结算收益</p>
    </div>

    <!-- 产出队列展示 -->
    <div v-if="gameStore.productionQueue.length > 0" class="production-queue-section">
      <h2>待结算收益</h2>
      <div class="queue-summary">
        <div class="summary-item">
          <div class="summary-icon">💰</div>
          <div class="summary-content">
            <span class="summary-label">金币</span>
            <span class="summary-value">{{ pendingMoney }}</span>
          </div>
        </div>
        <div class="summary-item">
          <div class="summary-icon">📦</div>
          <div class="summary-content">
            <span class="summary-label">材料</span>
            <span class="summary-value">{{ pendingMaterialsCount }}种</span>
          </div>
        </div>
        <div class="summary-item">
          <div class="summary-icon">⭐</div>
          <div class="summary-content">
            <span class="summary-label">经验</span>
            <span class="summary-value">{{ pendingExperienceCount }}人</span>
          </div>
        </div>
      </div>

      <!-- 详细队列列表 -->
      <div class="queue-details">
        <h3>详细列表</h3>
        <div class="queue-list">
          <div v-for="(item, index) in gameStore.productionQueue" :key="index" class="queue-item">
            <div class="item-icon">
              <span v-if="item.type === 'money'">💰</span>
              <span v-else-if="item.type === 'material'">📦</span>
              <span v-else-if="item.type === 'experience'">⭐</span>
            </div>
            <div class="item-content">
              <div class="item-title">
                <span v-if="item.type === 'money'">金币 +{{ item.amount }}</span>
                <span v-else-if="item.type === 'material'">{{ getMaterialName(item.materialType) }} +{{ item.amount
                  }}</span>
                <span v-else-if="item.type === 'experience'">{{ getCharacterName(item.characterId) }} 经验 +{{ item.amount
                  }}</span>
              </div>
              <div class="item-source">来源：{{ item.source }}</div>
            </div>
            <div class="item-day">第{{ item.day }}天</div>
          </div>
        </div>
      </div>

      <!-- 结算按钮 -->
      <div class="settlement-section">
        <a-button type="primary" size="large" @click="settleProduction" :loading="settling">
          结算所有收益
        </a-button>
      </div>
    </div>

    <!-- 无待结算收益 -->
    <div v-else class="no-production">
      <a-empty description="暂无待结算收益">
        <template #image>
          <div class="empty-icon">💤</div>
        </template>
      </a-empty>
    </div>

    <!-- 角色休息区域 -->
    <div class="rest-section">
      <h2>角色休息</h2>
      <div class="available-characters">
        <div v-if="availableCharactersForRest.length === 0" class="no-characters">
          <a-empty description="暂无可休息的角色" size="small" />
        </div>
        <div v-else class="character-grid">
          <div v-for="character in availableCharactersForRest" :key="character.id" class="character-rest-card">
            <div class="character-avatar">
              <img :src="character.portrait" :alt="character.name" />
            </div>
            <div class="character-info">
              <h4>{{ character.name }}</h4>
              <div class="character-stats">
                <div class="stat-item">
                  <span class="stat-label">体力</span>
                  <a-progress :percent="character.stamina" size="small"
                    :stroke-color="getStaminaColor(character.stamina)" />
                </div>
                <div class="stat-item">
                  <span class="stat-label">心情</span>
                  <a-progress :percent="character.mood" size="small" :stroke-color="getMoodColor(character.mood)" />
                </div>
              </div>
              <div class="character-status">
                <a-tag :color="getStatusColor(character.status)">{{ getStatusText(character.status) }}</a-tag>
              </div>
            </div>
            <div class="rest-actions">
              <a-button type="primary" size="small" @click="restCharacter(character.id)"
                :disabled="character.status === 'Resting'">
                {{ character.status === 'Resting' ? '休息中' : '开始休息' }}
              </a-button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { ArrowLeftOutlined } from '@ant-design/icons-vue'
import { useGameStore } from '@/stores/gameStore'
import { useCharacterStore } from '@/stores/characterStore'

const router = useRouter()
const gameStore = useGameStore()
const characterStore = useCharacterStore()

const settling = ref(false)

// 计算待结算的收益
const pendingMoney = computed(() => {
  return gameStore.productionQueue
    .filter(item => item.type === 'money')
    .reduce((sum, item) => sum + item.amount, 0)
})

const pendingMaterialsCount = computed(() => {
  const materials = new Set(
    gameStore.productionQueue
      .filter(item => item.type === 'material')
      .map(item => item.materialType)
  )
  return materials.size
})

const pendingExperienceCount = computed(() => {
  const characters = new Set(
    gameStore.productionQueue
      .filter(item => item.type === 'experience')
      .map(item => item.characterId)
  )
  return characters.size
})

// 可休息的角色
const availableCharactersForRest = computed(() => {
  return characterStore.characters.filter(character =>
    character.status !== 'Exiled' && !character.attributes.isAssigned
  )
})

// 获取材料名称
const getMaterialName = (materialType?: string): string => {
  const materialMap: Record<string, string> = {
    'seeds': '种子',
    'fruits': '灵果',
    'water': '圣水',
    'honey': '蜜酿',
    'milk': '乳液',
    'nectar': '琼浆'
  }
  return materialMap[materialType || ''] || materialType || '未知材料'
}

// 获取角色名称
const getCharacterName = (characterId?: string): string => {
  if (!characterId) return '未知角色'
  const character = characterStore.getCharacterById(characterId)
  return character?.name || '未知角色'
}

// 获取状态相关函数
const getStatusText = (status: string): string => {
  const statusMap: Record<string, string> = {
    'Normal': '正常',
    'Tired': '疲惫',
    'Overworked': '过劳',
    'Resting': '休息',
    'Exiled': '流放'
  }
  return statusMap[status] || status
}

const getStatusColor = (status: string): string => {
  const colorMap: Record<string, string> = {
    'Normal': 'green',
    'Tired': 'orange',
    'Overworked': 'red',
    'Resting': 'blue',
    'Exiled': 'default'
  }
  return colorMap[status] || 'default'
}

const getStaminaColor = (stamina: number): string => {
  if (stamina >= 70) return '#52c41a'
  if (stamina >= 40) return '#faad14'
  return '#ff4d4f'
}

const getMoodColor = (mood: number): string => {
  if (mood >= 70) return '#722ed1'
  if (mood >= 40) return '#faad14'
  return '#ff4d4f'
}

// 结算产出队列
const settleProduction = async () => {
  settling.value = true

  try {
    await new Promise(resolve => setTimeout(resolve, 1500))

    const result = gameStore.processProductionQueue()

    // 处理经验奖励
    Object.entries(result.experience).forEach(([characterId, exp]) => {
      characterStore.addExperience(characterId, exp)
    })

    // 显示结算结果
    let resultMessage = '收益结算完成！'
    if (result.money > 0) {
      resultMessage += ` 获得金币 ${result.money}`
    }
    if (Object.keys(result.materials).length > 0) {
      const materialText = Object.entries(result.materials)
        .map(([type, amount]) => `${getMaterialName(type)} ${amount}`)
        .join(', ')
      resultMessage += ` 获得材料：${materialText}`
    }

    message.success(resultMessage)

    // 保存游戏数据
    gameStore.saveGame()
    characterStore.saveCharacters()

  } catch (error) {
    message.error('结算过程中发生错误')
  } finally {
    settling.value = false
  }
}

// 角色休息
const restCharacter = (characterId: string) => {
  const success = characterStore.restCharacter(characterId, gameStore.currentDay)
  if (success) {
    message.success('角色开始休息')
    characterStore.saveCharacters()
  } else {
    message.error('无法让该角色休息')
  }
}
</script>

<style scoped>
.bedroom-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  text-align: center;
  margin-bottom: 30px;
  position: relative;
}

.back-button {
  position: absolute;
  left: 0;
  top: 0;
}

.page-header h1 {
  margin: 0;
  color: #333;
  font-size: 28px;
}

.subtitle {
  color: #666;
  margin: 8px 0 0 0;
}

.production-queue-section {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 30px;
}

.production-queue-section h2 {
  margin: 0 0 16px 0;
  color: #333;
}

.queue-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 20px;
}

.summary-item {
  display: flex;
  align-items: center;
  background: white;
  padding: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.summary-icon {
  font-size: 24px;
  margin-right: 12px;
}

.summary-content {
  display: flex;
  flex-direction: column;
}

.summary-label {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.summary-value {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.queue-details h3 {
  margin: 0 0 12px 0;
  color: #333;
  font-size: 16px;
}

.queue-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-height: 300px;
  overflow-y: auto;
}

.queue-item {
  display: flex;
  align-items: center;
  background: white;
  padding: 12px;
  border-radius: 6px;
  border: 1px solid #f0f0f0;
}

.item-icon {
  font-size: 16px;
  margin-right: 12px;
  width: 20px;
  text-align: center;
}

.item-content {
  flex: 1;
}

.item-title {
  font-weight: 500;
  color: #333;
  margin-bottom: 2px;
}

.item-source {
  font-size: 12px;
  color: #666;
}

.item-day {
  font-size: 11px;
  color: #999;
  background: #f5f5f5;
  padding: 2px 6px;
  border-radius: 4px;
}

.settlement-section {
  text-align: center;
  margin-top: 20px;
}

.no-production {
  text-align: center;
  padding: 60px 20px;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.rest-section {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.rest-section h2 {
  margin: 0 0 20px 0;
  color: #333;
}

.character-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
}

.character-rest-card {
  display: flex;
  align-items: center;
  background: #fafafa;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid #f0f0f0;
}

.character-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 12px;
  flex-shrink: 0;
}

.character-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.character-info {
  flex: 1;
  margin-right: 12px;
}

.character-info h4 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 14px;
}

.character-stats {
  margin-bottom: 8px;
}

.stat-item {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 11px;
  color: #666;
  width: 30px;
  margin-right: 8px;
}

.character-status {
  margin-top: 4px;
}

.rest-actions {
  flex-shrink: 0;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .bedroom-container {
    padding: 16px;
  }

  .queue-summary {
    grid-template-columns: 1fr;
  }

  .character-grid {
    grid-template-columns: 1fr;
  }

  .character-rest-card {
    flex-direction: column;
    text-align: center;
  }

  .character-avatar {
    margin: 0 0 12px 0;
  }

  .character-info {
    margin: 0 0 12px 0;
  }
}
</style>
