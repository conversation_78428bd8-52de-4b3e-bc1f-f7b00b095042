<template>
  <div class="game-map">
    <div class="map-header">
      <h2>游戏地图</h2>
      <div class="time-info">
        <a-tag color="blue" class="time-tag">
          第{{ gameStore.currentDay }}天 {{ gameStore.currentTimeSlot }}
        </a-tag>
        <!-- 测试按钮 -->
        <a-button size="small" @click="addTestProduction" style="margin-left: 8px;">
          测试产出队列
        </a-button>
      </div>
    </div>

    <div class="map-content">
      <!-- 根据时间段显示不同的场景入口 -->
      <div v-if="gameStore.currentTimeSlot === '上午'" class="time-section">
        <h3>上午时段</h3>
        <div class="location-grid">
          <div class="location-card" @click="enterLocation('llyzf')">
            <div class="location-icon">🏛️</div>
            <h4>琉璃胭脂坊</h4>
            <p>生产琼浆玉液的核心场所</p>
          </div>
        </div>
      </div>

      <div v-else-if="gameStore.currentTimeSlot === '下午'" class="time-section">
        <h3>下午时段</h3>
        <div class="location-grid">
          <div class="location-card" @click="enterLocation('hspmh')">
            <div class="location-icon">🏪</div>
            <h4>黑市拍卖会</h4>
            <p>仅在周六开放</p>
            <a-tag v-if="!isWeekend" color="red" size="small">未开放</a-tag>
          </div>

          <div class="location-card" @click="enterLocation('fcsh')">
            <div class="location-icon">🏢</div>
            <h4>翡翠商会</h4>
            <p>购买材料，出售商品</p>
          </div>

          <div class="location-card" @click="enterLocation('gdy')">
            <div class="location-icon">🌸</div>
            <h4>孤独园</h4>
            <p>招募新的少女</p>
          </div>

          <div class="location-card" @click="enterLocation('farm')">
            <div class="location-icon">🌾</div>
            <h4>农场</h4>
            <p>种植和收获灵果</p>
          </div>
        </div>
      </div>

      <div v-else-if="gameStore.currentTimeSlot === '夜晚'" class="time-section">
        <h3>夜晚时段</h3>
        <div class="location-grid">
          <div class="location-card" @click="enterLocation('bedroom')">
            <div class="location-icon">🛏️</div>
            <h4>寝室</h4>
            <p>休息和恢复体力</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 时间推进按钮 -->
    <div class="time-controls">
      <a-button type="primary" size="large" @click="advanceTime" :loading="timeAdvancing">
        推进时间
      </a-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { useGameStore } from '@/stores/gameStore'
import { useCharacterStore } from '@/stores/characterStore'

const router = useRouter()
const gameStore = useGameStore()
const characterStore = useCharacterStore()
const timeAdvancing = ref(false)

// 计算是否为周末
const isWeekend = computed(() => {
  return gameStore.isWeekend
})

// 进入指定场所
const enterLocation = (locationKey: string) => {
  const locationMap: Record<string, string> = {
    llyzf: '琉璃胭脂坊',
    hspmh: '黑市拍卖会',
    fcsh: '翡翠商会',
    gdy: '孤独园',
    farm: '农场',
    bedroom: '寝室'
  }

  const locationName = locationMap[locationKey]

  // 检查特殊条件
  if (locationKey === 'hspmh' && !isWeekend.value) {
    message.warning('黑市拍卖会仅在周六开放')
    return
  }

  if (locationKey === 'llyzf' && gameStore.currentTimeSlot === '上午') {
    // 跳转到琉璃胭脂坊
    router.push('/llyzf')
    return
  }

  if (locationKey === 'bedroom' && gameStore.currentTimeSlot === '夜晚') {
    // 跳转到寝室
    router.push('/bedroom')
    return
  }

  message.info(`进入${locationName}（功能开发中）`)
}

// 测试产出队列
const addTestProduction = () => {
  // 添加一些测试产出项目
  gameStore.addToProductionQueue({
    type: 'money',
    amount: 100,
    source: '测试金币产出'
  })

  gameStore.addToProductionQueue({
    type: 'material',
    amount: 5,
    materialType: 'honey',
    source: '测试蜜酿产出'
  })

  gameStore.addToProductionQueue({
    type: 'material',
    amount: 3,
    materialType: 'nectar',
    source: '测试琼浆产出'
  })

  message.success('已添加测试产出到队列，请查看右上角的时钟图标')
}

// 推进时间
const advanceTime = () => {
  timeAdvancing.value = true

  setTimeout(() => {
    const wasNight = gameStore.currentTimeSlot === '夜晚'
    const wasAfternoon = gameStore.currentTimeSlot === '下午'
    const result = gameStore.advanceTime()

    // 如果是从下午推进到夜晚，结束所有派遣
    if (wasAfternoon && gameStore.currentTimeSlot === '夜晚') {
      const endedCount = characterStore.endAllDispatches(gameStore.currentDay)
      if (endedCount > 0) {
        message.info(`${endedCount}名角色完成派遣任务`)
      }
    }

    // 如果是从夜晚推进到新的一天，触发角色每日恢复
    if (wasNight) {
      characterStore.dailyRecovery(gameStore.currentDay)
    }

    message.success(result.message)

    // 自动保存游戏
    gameStore.saveGame()
    characterStore.saveCharacters()

    timeAdvancing.value = false
  }, 1000)
}
</script>

<style scoped>
.game-map {
  background: white;
  border-radius: 12px;
  padding: 24px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.map-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.map-header h2 {
  margin: 0;
  color: #333;
}

.time-tag {
  font-size: 14px;
  font-weight: bold;
}

.map-content {
  flex: 1;
  overflow-y: auto;
}

.time-section {
  margin-bottom: 32px;
}

.time-section h3 {
  color: var(--nectar-purple);
  margin-bottom: 16px;
  font-size: 18px;
}

.location-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.location-card {
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border: 2px solid transparent;
  border-radius: 12px;
  padding: 20px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.location-card:hover {
  border-color: var(--nectar-purple);
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
  background: linear-gradient(135deg, #fff, #f8f9fa);
}

.location-icon {
  font-size: 48px;
  margin-bottom: 12px;
}

.location-card h4 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 16px;
  font-weight: bold;
}

.location-card p {
  margin: 0;
  color: #666;
  font-size: 14px;
  line-height: 1.4;
}

.location-card .ant-tag {
  position: absolute;
  top: 8px;
  right: 8px;
}

.time-controls {
  margin-top: 24px;
  text-align: center;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .game-map {
    padding: 16px;
  }

  .map-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .location-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .location-card {
    padding: 16px;
  }

  .location-icon {
    font-size: 36px;
  }
}
</style>
