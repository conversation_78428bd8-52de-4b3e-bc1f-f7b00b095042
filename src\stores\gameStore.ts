import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import dayjs from 'dayjs'
import { createDispatchModule, type DispatchModule } from './modules/dispatchModule'
import { createPlayerStatsModule, type PlayerStatsModule } from './modules/playerStatsModule'
import { createInventoryModule, type InventoryModule } from './modules/inventoryModule'
import { createProductionQueueModule, type ProductionQueueModule } from './modules/productionQueueModule'

export type TimeSlot = '上午' | '下午' | '夜晚'

export const useGameStore = defineStore('game', () => {
  // 游戏时间状态
  const currentDay = ref<number>(1)
  const currentTimeSlot = ref<TimeSlot>('上午')

  // 游戏开始日期（2025年6月1日）
  const gameStartDate = ref(dayjs('2025-06-01'))

  // 创建模块实例
  const dispatchModule = createDispatchModule()
  const playerStatsModule = createPlayerStatsModule()
  const inventoryModule = createInventoryModule()
  const productionQueueModule = createProductionQueueModule()

  // 计算属性：当前游戏日期
  const currentGameDate = computed(() => {
    return gameStartDate.value.add(currentDay.value - 1, 'day')
  })

  // 计算属性：是否为周末
  const isWeekend = computed(() => {
    const dayOfWeek = currentGameDate.value.day()
    return dayOfWeek === 0 || dayOfWeek === 6 // 0=周日, 6=周六
  })

  // 计算属性：格式化的日期显示
  const formattedDate = computed(() => {
    return currentGameDate.value.format('YYYY年MM月DD日')
  })

  // 计算属性：星期显示
  const dayOfWeekText = computed(() => {
    const days = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
    return days[currentGameDate.value.day()]
  })

  // 计算属性：完整的时间显示
  const fullTimeDisplay = computed(() => {
    return `${formattedDate.value} ${dayOfWeekText.value} ${currentTimeSlot.value}`
  })

  // 推进时间
  function advanceTime(): { success: boolean; message: string } {
    const currentSlot = currentTimeSlot.value

    if (currentSlot === '上午') {
      currentTimeSlot.value = '下午'
      return {
        success: true,
        message: '时间推进到下午'
      }
    } else if (currentSlot === '下午') {
      currentTimeSlot.value = '夜晚'

      // 当推进到夜晚时，结束所有角色的派遣状态
      dispatchModule.endAllDispatches()

      return {
        success: true,
        message: '时间推进到夜晚，所有派遣任务已完成'
      }
    } else {
      // 夜晚推进到下一天的上午
      currentDay.value += 1
      currentTimeSlot.value = '上午'

      // 每日恢复体力
      playerStatsModule.restoreEnergyDaily(currentDay.value)

      // 清空派遣队列（新的一天开始）
      dispatchModule.clearDispatchQueue()

      // 清理历史记录
      playerStatsModule.cleanupHistory(currentDay.value)
      inventoryModule.cleanupHistory(currentDay.value)
      productionQueueModule.cleanupHistory(currentDay.value)

      // 触发角色的每日恢复（需要从characterStore调用）
      // 这里需要在组件中调用characterStore.dailyRecovery(currentDay.value)

      return {
        success: true,
        message: `新的一天开始了！${formattedDate.value}`
      }
    }
  }

  // 便捷方法：委托给模块
  function restoreEnergyDaily() {
    playerStatsModule.restoreEnergyDaily(currentDay.value)
  }

  function consumeEnergy(amount: number, reason?: string): boolean {
    return playerStatsModule.consumeEnergy(amount, reason || '消耗体力', currentDay.value)
  }

  function restoreEnergy(amount: number, reason?: string) {
    playerStatsModule.restoreEnergy(amount, reason || '恢复体力', currentDay.value)
  }

  function addMoney(amount: number, reason?: string) {
    playerStatsModule.addMoney(amount, reason || '获得金币', currentDay.value)
  }

  function spendMoney(amount: number, reason?: string): boolean {
    return playerStatsModule.spendMoney(amount, reason || '消费金币', currentDay.value)
  }

  function addGems(amount: number, reason?: string) {
    playerStatsModule.addGems(amount, reason || '获得灵石', currentDay.value)
  }

  function spendGems(amount: number, reason?: string): boolean {
    return playerStatsModule.spendGems(amount, reason || '消费灵石', currentDay.value)
  }

  function addMaterial(type: string, amount: number, reason?: string) {
    inventoryModule.addMaterial(type as any, amount, reason || '获得材料', currentDay.value)
  }

  function consumeMaterial(type: string, amount: number, reason?: string): boolean {
    return inventoryModule.consumeMaterial(type as any, amount, reason || '消耗材料', currentDay.value)
  }

  // 保存游戏状态
  function saveGame() {
    const gameState = {
      currentDay: currentDay.value,
      currentTimeSlot: currentTimeSlot.value,
      playerResources: playerStatsModule.playerResources.value,
      materials: inventoryModule.materials.value,
      dispatchQueue: dispatchModule.dispatchQueue.value,
      productionQueue: productionQueueModule.productionQueue.value,
      savedAt: new Date().toISOString(),
    }

    localStorage.setItem('nectar-game-save', JSON.stringify(gameState))
  }

  // 加载游戏状态
  function loadGame(): boolean {
    try {
      const saved = localStorage.getItem('nectar-game-save')
      if (saved) {
        const gameState = JSON.parse(saved)
        currentDay.value = gameState.currentDay || 1
        currentTimeSlot.value = gameState.currentTimeSlot || '上午'

        // 加载模块状态
        if (gameState.playerResources) {
          Object.assign(playerStatsModule.playerResources.value, gameState.playerResources)
        }
        if (gameState.materials) {
          Object.assign(inventoryModule.materials.value, gameState.materials)
        }
        if (gameState.dispatchQueue) {
          dispatchModule.dispatchQueue.value = gameState.dispatchQueue
        }
        if (gameState.productionQueue) {
          productionQueueModule.productionQueue.value = gameState.productionQueue
        }

        return true
      }
    } catch (error) {
      console.error('Failed to load game:', error)
    }
    return false
  }

  // 派遣相关便捷方法：委托给派遣模块
  function addToDispatchQueue(characterId: string, workshopType: string, activity: string) {
    dispatchModule.addToDispatchQueue(characterId, workshopType, activity, currentDay.value, currentTimeSlot.value)
  }

  function removeFromDispatchQueue(characterId: string) {
    dispatchModule.removeFromDispatchQueue(characterId)
  }

  function clearDispatchQueue() {
    dispatchModule.clearDispatchQueue()
  }

  function endAllDispatches() {
    return dispatchModule.endAllDispatches()
  }

  // 产出队列相关便捷方法：委托给产出队列模块
  function addToProductionQueue(item: {
    type: 'material' | 'money' | 'experience'
    characterId?: string
    amount: number
    materialType?: string
    source: string
  }) {
    productionQueueModule.addToProductionQueue(item, currentDay.value, currentTimeSlot.value)
  }

  // 处理产出队列（在寝室休息时调用）
  function processProductionQueue(): { money: number; materials: Record<string, number>; experience: Record<string, number> } {
    const result = productionQueueModule.processProductionQueue()

    // 应用奖励到实际资源
    if (result.money > 0) {
      addMoney(result.money, '产出队列结算')
    }

    Object.entries(result.materials).forEach(([materialType, amount]) => {
      addMaterial(materialType, amount, '产出队列结算')
    })

    return result
  }

  // 重置游戏状态
  function resetGame() {
    currentDay.value = 1
    currentTimeSlot.value = '上午'

    // 重置各模块
    playerStatsModule.resetPlayerResources()
    inventoryModule.resetInventory()
    dispatchModule.clearDispatchQueue()
    productionQueueModule.resetProductionQueue()

    // 清除存档
    localStorage.removeItem('nectar-game-save')
  }

  return {
    // 核心状态
    currentDay,
    currentTimeSlot,
    gameStartDate,

    // 模块状态（通过模块访问）
    playerResources: playerStatsModule.playerResources,
    materials: inventoryModule.materials,
    dispatchQueue: dispatchModule.dispatchQueue,
    productionQueue: productionQueueModule.productionQueue,

    // 计算属性
    currentGameDate,
    isWeekend,
    formattedDate,
    dayOfWeekText,
    fullTimeDisplay,

    // 产出队列计算属性
    pendingQueue: productionQueueModule.pendingQueue,
    pendingMoney: productionQueueModule.pendingMoney,
    pendingMaterialsCount: productionQueueModule.pendingMaterialsCount,
    pendingExperienceCount: productionQueueModule.pendingExperienceCount,

    // 核心方法
    advanceTime,
    saveGame,
    loadGame,
    resetGame,

    // 便捷方法（委托给模块）
    restoreEnergyDaily,
    consumeEnergy,
    restoreEnergy,
    addMoney,
    spendMoney,
    addGems,
    spendGems,
    addMaterial,
    consumeMaterial,
    addToDispatchQueue,
    removeFromDispatchQueue,
    clearDispatchQueue,
    endAllDispatches,
    addToProductionQueue,
    processProductionQueue,

    // 模块访问（用于高级功能）
    dispatch: dispatchModule,
    playerStats: playerStatsModule,
    inventory: inventoryModule,
    production: productionQueueModule,
  }
})
