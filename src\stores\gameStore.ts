import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import dayjs from 'dayjs'

export type TimeSlot = '上午' | '下午' | '夜晚'

export const useGameStore = defineStore('game', () => {
  // 游戏时间状态
  const currentDay = ref<number>(1)
  const currentTimeSlot = ref<TimeSlot>('上午')
  
  // 游戏开始日期（2025年6月1日）
  const gameStartDate = ref(dayjs('2025-06-01'))
  
  // 玩家资源
  const playerResources = ref({
    money: 1000,        // 金币
    gems: 50,           // 灵石
    energy: 80,         // 体力
    maxEnergy: 100,     // 最大体力
  })
  
  // 材料库存
  const materials = ref({
    seeds: 10,          // 种子
    fruits: 5,          // 灵果
    water: 20,          // 圣水
    honey: 15,          // 蜜酿
    milk: 8,            // 乳液
    nectar: 3,          // 琼浆
  })
  
  // 计算属性：当前游戏日期
  const currentGameDate = computed(() => {
    return gameStartDate.value.add(currentDay.value - 1, 'day')
  })
  
  // 计算属性：是否为周末
  const isWeekend = computed(() => {
    const dayOfWeek = currentGameDate.value.day()
    return dayOfWeek === 0 || dayOfWeek === 6 // 0=周日, 6=周六
  })
  
  // 计算属性：格式化的日期显示
  const formattedDate = computed(() => {
    return currentGameDate.value.format('YYYY年MM月DD日')
  })
  
  // 计算属性：星期显示
  const dayOfWeekText = computed(() => {
    const days = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
    return days[currentGameDate.value.day()]
  })
  
  // 计算属性：完整的时间显示
  const fullTimeDisplay = computed(() => {
    return `${formattedDate.value} ${dayOfWeekText.value} ${currentTimeSlot.value}`
  })
  
  // 推进时间
  function advanceTime(): { success: boolean; message: string } {
    const currentSlot = currentTimeSlot.value
    
    if (currentSlot === '上午') {
      currentTimeSlot.value = '下午'
      return {
        success: true,
        message: '时间推进到下午'
      }
    } else if (currentSlot === '下午') {
      currentTimeSlot.value = '夜晚'
      return {
        success: true,
        message: '时间推进到夜晚'
      }
    } else {
      // 夜晚推进到下一天的上午
      currentDay.value += 1
      currentTimeSlot.value = '上午'
      
      // 每日恢复体力
      restoreEnergyDaily()
      
      return {
        success: true,
        message: `新的一天开始了！${formattedDate.value}`
      }
    }
  }
  
  // 每日体力恢复
  function restoreEnergyDaily() {
    playerResources.value.energy = Math.min(
      playerResources.value.energy + 30,
      playerResources.value.maxEnergy
    )
  }
  
  // 消耗体力
  function consumeEnergy(amount: number): boolean {
    if (playerResources.value.energy >= amount) {
      playerResources.value.energy -= amount
      return true
    }
    return false
  }
  
  // 恢复体力
  function restoreEnergy(amount: number) {
    playerResources.value.energy = Math.min(
      playerResources.value.energy + amount,
      playerResources.value.maxEnergy
    )
  }
  
  // 增加金币
  function addMoney(amount: number) {
    playerResources.value.money += amount
  }
  
  // 消耗金币
  function spendMoney(amount: number): boolean {
    if (playerResources.value.money >= amount) {
      playerResources.value.money -= amount
      return true
    }
    return false
  }
  
  // 增加灵石
  function addGems(amount: number) {
    playerResources.value.gems += amount
  }
  
  // 消耗灵石
  function spendGems(amount: number): boolean {
    if (playerResources.value.gems >= amount) {
      playerResources.value.gems -= amount
      return true
    }
    return false
  }
  
  // 增加材料
  function addMaterial(type: keyof typeof materials.value, amount: number) {
    materials.value[type] += amount
  }
  
  // 消耗材料
  function consumeMaterial(type: keyof typeof materials.value, amount: number): boolean {
    if (materials.value[type] >= amount) {
      materials.value[type] -= amount
      return true
    }
    return false
  }
  
  // 保存游戏状态
  function saveGame() {
    const gameState = {
      currentDay: currentDay.value,
      currentTimeSlot: currentTimeSlot.value,
      playerResources: playerResources.value,
      materials: materials.value,
      savedAt: new Date().toISOString(),
    }
    
    localStorage.setItem('nectar-game-save', JSON.stringify(gameState))
  }
  
  // 加载游戏状态
  function loadGame(): boolean {
    try {
      const saved = localStorage.getItem('nectar-game-save')
      if (saved) {
        const gameState = JSON.parse(saved)
        currentDay.value = gameState.currentDay || 1
        currentTimeSlot.value = gameState.currentTimeSlot || '上午'
        playerResources.value = { ...playerResources.value, ...gameState.playerResources }
        materials.value = { ...materials.value, ...gameState.materials }
        return true
      }
    } catch (error) {
      console.error('Failed to load game:', error)
    }
    return false
  }
  
  // 重置游戏状态
  function resetGame() {
    currentDay.value = 1
    currentTimeSlot.value = '上午'
    playerResources.value = {
      money: 1000,
      gems: 50,
      energy: 80,
      maxEnergy: 100,
    }
    materials.value = {
      seeds: 10,
      fruits: 5,
      water: 20,
      honey: 15,
      milk: 8,
      nectar: 3,
    }
    
    // 清除存档
    localStorage.removeItem('nectar-game-save')
  }
  
  return {
    // 状态
    currentDay,
    currentTimeSlot,
    gameStartDate,
    playerResources,
    materials,
    
    // 计算属性
    currentGameDate,
    isWeekend,
    formattedDate,
    dayOfWeekText,
    fullTimeDisplay,
    
    // 方法
    advanceTime,
    restoreEnergyDaily,
    consumeEnergy,
    restoreEnergy,
    addMoney,
    spendMoney,
    addGems,
    spendGems,
    addMaterial,
    consumeMaterial,
    saveGame,
    loadGame,
    resetGame,
  }
})
